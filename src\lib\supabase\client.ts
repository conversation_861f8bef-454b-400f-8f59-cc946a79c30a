import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    flowType: 'pkce' // Use PKCE for better security
  }
});

// Auth helper functions
export const auth = {
  // Sign up with email and password (NO EMAIL VERIFICATION)
  signUp: async (email: string, password: string, userData?: any) => {
    console.log('Starting sign up process for:', email);

    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: userData,
        // Remove email verification requirement
        emailRedirectTo: undefined
      }
    });

    if (error) {
      console.error('Sign up error:', error);
    } else {
      console.log('Sign up successful (no email verification required):', data);
    }

    return { data, error };
  },

  // Sign in with email and password
  signIn: async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });
    return { data, error };
  },

  // Sign in with magic link
  signInWithMagicLink: async (email: string) => {
    console.log('Sending magic link to:', email);

    const { data, error } = await supabase.auth.signInWithOtp({
      email,
      options: {
        emailRedirectTo: `${window.location.origin}/auth/callback`,
        shouldCreateUser: true // Allow creating new users via magic link
      }
    });

    if (error) {
      console.error('Magic link error:', error);
    } else {
      console.log('Magic link sent successfully:', data);
    }

    return { data, error };
  },

  // Sign in with Google OAuth
  signInWithGoogle: async () => {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/auth/callback`,
        queryParams: {
          access_type: 'offline',
          prompt: 'consent',
        }
      }
    });
    return { data, error };
  },

  // Sign out
  signOut: async () => {
    const { error } = await supabase.auth.signOut();
    return { error };
  },

  // Get current session
  getSession: async () => {
    const { data: { session }, error } = await supabase.auth.getSession();
    return { session, error };
  },

  // Get current user
  getUser: async () => {
    const { data: { user }, error } = await supabase.auth.getUser();
    return { user, error };
  },

  // Listen to auth changes
  onAuthStateChange: (callback: (event: string, session: any) => void) => {
    return supabase.auth.onAuthStateChange(callback);
  },

  // Reset password
  resetPassword: async (email: string) => {
    const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/auth/reset-password`
    });
    return { data, error };
  },

  // Update password
  updatePassword: async (password: string) => {
    const { data, error } = await supabase.auth.updateUser({
      password
    });
    return { data, error };
  },

  // Sign in with Web3 wallet (Solana)
  signInWithWallet: async (walletAddress: string, signature: string, message: string) => {
    try {
      // Create a unique email for the wallet
      const walletEmail = `${walletAddress}@solana.wallet`;

      // First, try to sign in with the wallet email
      const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
        email: walletEmail,
        password: signature // Use signature as password
      });

      if (signInData.user) {
        return { data: signInData, error: null };
      }

      // If sign in fails, create a new account
      if (signInError) {
        console.log('Creating new wallet account...');
        const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
          email: walletEmail,
          password: signature,
          options: {
            data: {
              wallet_address: walletAddress,
              wallet_type: 'solana',
              display_name: `Wallet ${walletAddress.slice(0, 8)}...${walletAddress.slice(-4)}`
            }
          }
        });

        return { data: signUpData, error: signUpError };
      }

      return { data: null, error: signInError };
    } catch (error) {
      console.error('Wallet authentication error:', error);
      return { data: null, error };
    }
  }
};

export default supabase;
