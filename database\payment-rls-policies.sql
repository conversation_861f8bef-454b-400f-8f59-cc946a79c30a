-- =====================================================
-- KOI APP PAYMENT SYSTEM RLS POLICIES
-- Enterprise-grade security for payment data
-- =====================================================

-- Enable RLS on all payment tables
ALTER TABLE subscription_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_methods ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE billing_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_webhooks ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscription_usage ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- SUBSCRIPTION PLANS POLICIES (Public read, admin write)
-- =====================================================

-- Allow all authenticated users to read active subscription plans
CREATE POLICY "Users can view active subscription plans" ON subscription_plans
  FOR SELECT
  TO authenticated
  USING (is_active = true);

-- Allow service role to manage subscription plans
CREATE POLICY "Service role can manage subscription plans" ON subscription_plans
  FOR ALL
  TO service_role
  USING (true);

-- =====================================================
-- USER SUBSCRIPTIONS POLICIES (User owns their data)
-- =====================================================

-- Users can view their own subscriptions
CREATE POLICY "Users can view own subscriptions" ON user_subscriptions
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

-- Users can update their own subscription settings (limited fields)
CREATE POLICY "Users can update own subscription settings" ON user_subscriptions
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (
    auth.uid() = user_id AND
    -- Only allow updating these fields
    (OLD.plan_id = NEW.plan_id) AND
    (OLD.user_id = NEW.user_id) AND
    (OLD.stripe_subscription_id = NEW.stripe_subscription_id) AND
    (OLD.paypal_subscription_id = NEW.paypal_subscription_id)
  );

-- Service role can manage all subscriptions
CREATE POLICY "Service role can manage subscriptions" ON user_subscriptions
  FOR ALL
  TO service_role
  USING (true);

-- =====================================================
-- PAYMENT METHODS POLICIES (User owns their data)
-- =====================================================

-- Users can view their own payment methods
CREATE POLICY "Users can view own payment methods" ON payment_methods
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

-- Users can insert their own payment methods
CREATE POLICY "Users can add own payment methods" ON payment_methods
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- Users can update their own payment methods (limited fields)
CREATE POLICY "Users can update own payment methods" ON payment_methods
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (
    auth.uid() = user_id AND
    -- Only allow updating these fields
    (OLD.user_id = NEW.user_id) AND
    (OLD.stripe_payment_method_id = NEW.stripe_payment_method_id) AND
    (OLD.paypal_payment_method_id = NEW.paypal_payment_method_id) AND
    (OLD.solana_wallet_address = NEW.solana_wallet_address)
  );

-- Users can delete their own payment methods
CREATE POLICY "Users can delete own payment methods" ON payment_methods
  FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);

-- Service role can manage all payment methods
CREATE POLICY "Service role can manage payment methods" ON payment_methods
  FOR ALL
  TO service_role
  USING (true);

-- =====================================================
-- PAYMENT TRANSACTIONS POLICIES (User owns their data)
-- =====================================================

-- Users can view their own transactions
CREATE POLICY "Users can view own transactions" ON payment_transactions
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

-- Service role can manage all transactions
CREATE POLICY "Service role can manage transactions" ON payment_transactions
  FOR ALL
  TO service_role
  USING (true);

-- =====================================================
-- BILLING HISTORY POLICIES (User owns their data)
-- =====================================================

-- Users can view their own billing history
CREATE POLICY "Users can view own billing history" ON billing_history
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

-- Service role can manage all billing history
CREATE POLICY "Service role can manage billing history" ON billing_history
  FOR ALL
  TO service_role
  USING (true);

-- =====================================================
-- PAYMENT WEBHOOKS POLICIES (Service role only)
-- =====================================================

-- Only service role can access webhooks (security sensitive)
CREATE POLICY "Service role only for webhooks" ON payment_webhooks
  FOR ALL
  TO service_role
  USING (true);

-- =====================================================
-- SUBSCRIPTION USAGE POLICIES (User owns their data)
-- =====================================================

-- Users can view their own usage data
CREATE POLICY "Users can view own usage" ON subscription_usage
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

-- Service role can manage all usage data
CREATE POLICY "Service role can manage usage" ON subscription_usage
  FOR ALL
  TO service_role
  USING (true);

-- =====================================================
-- ADDITIONAL SECURITY FUNCTIONS
-- =====================================================

-- Function to check if user has active subscription
CREATE OR REPLACE FUNCTION user_has_active_subscription(user_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM user_subscriptions 
    WHERE user_id = user_uuid 
    AND status IN ('trial', 'active')
    AND current_period_end > NOW()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's subscription status
CREATE OR REPLACE FUNCTION get_user_subscription_status(user_uuid UUID)
RETURNS TABLE (
  status VARCHAR(20),
  plan_name VARCHAR(100),
  current_period_end TIMESTAMP WITH TIME ZONE,
  trial_end TIMESTAMP WITH TIME ZONE,
  cancel_at_period_end BOOLEAN
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    us.status,
    sp.name as plan_name,
    us.current_period_end,
    us.trial_end,
    us.cancel_at_period_end
  FROM user_subscriptions us
  JOIN subscription_plans sp ON us.plan_id = sp.id
  WHERE us.user_id = user_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check feature access
CREATE OR REPLACE FUNCTION user_can_access_feature(user_uuid UUID, feature_name VARCHAR(50))
RETURNS BOOLEAN AS $$
DECLARE
  subscription_active BOOLEAN;
  usage_count INTEGER;
  usage_limit INTEGER;
BEGIN
  -- Check if user has active subscription
  SELECT user_has_active_subscription(user_uuid) INTO subscription_active;
  
  IF NOT subscription_active THEN
    RETURN FALSE;
  END IF;
  
  -- Check usage limits for current period
  SELECT 
    COALESCE(su.usage_count, 0),
    COALESCE(su.usage_limit, 999999)
  INTO usage_count, usage_limit
  FROM subscription_usage su
  WHERE su.user_id = user_uuid 
  AND su.feature = feature_name
  AND su.period_start <= NOW()
  AND su.period_end > NOW();
  
  RETURN usage_count < usage_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- GRANT PERMISSIONS
-- =====================================================

-- Grant execute permissions on security functions
GRANT EXECUTE ON FUNCTION user_has_active_subscription(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_subscription_status(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION user_can_access_feature(UUID, VARCHAR(50)) TO authenticated;

-- Grant usage on sequences
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO service_role;

-- =====================================================
-- AUDIT TRIGGERS (Optional - for compliance)
-- =====================================================

-- Create audit log table
CREATE TABLE payment_audit_log (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  table_name VARCHAR(50) NOT NULL,
  record_id UUID NOT NULL,
  action VARCHAR(10) NOT NULL, -- INSERT, UPDATE, DELETE
  old_values JSONB,
  new_values JSONB,
  user_id UUID,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Audit trigger function
CREATE OR REPLACE FUNCTION audit_payment_changes()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'DELETE' THEN
    INSERT INTO payment_audit_log (table_name, record_id, action, old_values, user_id)
    VALUES (TG_TABLE_NAME, OLD.id, 'DELETE', row_to_json(OLD), auth.uid());
    RETURN OLD;
  ELSIF TG_OP = 'UPDATE' THEN
    INSERT INTO payment_audit_log (table_name, record_id, action, old_values, new_values, user_id)
    VALUES (TG_TABLE_NAME, NEW.id, 'UPDATE', row_to_json(OLD), row_to_json(NEW), auth.uid());
    RETURN NEW;
  ELSIF TG_OP = 'INSERT' THEN
    INSERT INTO payment_audit_log (table_name, record_id, action, new_values, user_id)
    VALUES (TG_TABLE_NAME, NEW.id, 'INSERT', row_to_json(NEW), auth.uid());
    RETURN NEW;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Apply audit triggers to sensitive tables
CREATE TRIGGER audit_user_subscriptions AFTER INSERT OR UPDATE OR DELETE ON user_subscriptions FOR EACH ROW EXECUTE FUNCTION audit_payment_changes();
CREATE TRIGGER audit_payment_transactions AFTER INSERT OR UPDATE OR DELETE ON payment_transactions FOR EACH ROW EXECUTE FUNCTION audit_payment_changes();
CREATE TRIGGER audit_payment_methods AFTER INSERT OR UPDATE OR DELETE ON payment_methods FOR EACH ROW EXECUTE FUNCTION audit_payment_changes();
