'use client';

import { useState } from 'react';
import { Wallet } from 'lucide-react';

interface Web3WalletButtonProps {
  onConnect?: (walletAddress: string) => void;
  disabled?: boolean;
}

export const Web3WalletButton = ({ onConnect, disabled }: Web3WalletButtonProps) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const connectWallet = async () => {
    setLoading(true);
    setError('');

    try {
      // Check if Solana wallet is available
      if (typeof window !== 'undefined' && (window as any).solana) {
        const solana = (window as any).solana;

        // Check if it's Phantom wallet or other Solana wallets
        if (solana.isPhantom || solana.isSolflare || solana.publicKey) {
          console.log('Solana wallet detected:', {
            isPhantom: solana.isPhantom,
            isSolflare: solana.isSolflare,
            hasPublicKey: !!solana.publicKey
          });

          // Check if already connected
          if (solana.isConnected && solana.publicKey) {
            console.log('Wallet already connected');
            const walletAddress = solana.publicKey.toString();
            onConnect?.(walletAddress);
            return;
          }

          // Request connection with timeout
          const connectPromise = solana.connect({ onlyIfTrusted: false });
          const timeoutPromise = new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Connection timeout')), 10000)
          );

          const response = await Promise.race([connectPromise, timeoutPromise]);

          if (response && response.publicKey) {
            const walletAddress = response.publicKey.toString();
            console.log('Connected to wallet:', walletAddress);
            onConnect?.(walletAddress);
          } else {
            throw new Error('No public key received from wallet');
          }

        } else {
          setError('Please install a Solana wallet (Phantom, Solflare, etc.)');
        }
      } else {
        setError('No Solana wallet detected. Please install Phantom or Solflare wallet.');
      }
    } catch (err: any) {
      console.error('Wallet connection error:', err);
      if (err.code === 4001 || err.message?.includes('rejected')) {
        setError('Connection rejected by user');
      } else if (err.message?.includes('timeout')) {
        setError('Connection timeout. Please try again.');
      } else {
        setError('Failed to connect wallet. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-2">
      <button
        type="button"
        onClick={connectWallet}
        disabled={loading || disabled}
        className="w-full bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-400 hover:to-purple-500 text-white font-semibold py-2 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 text-sm"
      >
        {loading ? (
          <div className="w-3.5 h-3.5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
        ) : (
          <>
            <Wallet className="w-4 h-4" />
            <span>Connect Solana Wallet</span>
          </>
        )}
      </button>
      
      {error && (
        <p className="text-red-300 text-xs text-center">{error}</p>
      )}
      
      <p className="text-blue-200 text-xs text-center">
        Supports Phantom, Solflare, and other Solana wallets
      </p>
    </div>
  );
};
