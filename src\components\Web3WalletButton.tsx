'use client';

import { useState } from 'react';
import { Wallet } from 'lucide-react';

interface Web3WalletButtonProps {
  onConnect?: (walletAddress: string) => void;
  disabled?: boolean;
}

export const Web3WalletButton = ({ onConnect, disabled }: Web3WalletButtonProps) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const connectWallet = async () => {
    setLoading(true);
    setError('');

    try {
      // Check if Solana wallet is available
      if (typeof window !== 'undefined' && (window as any).solana) {
        const solana = (window as any).solana;
        
        // Check if it's Phantom wallet
        if (solana.isPhantom) {
          console.log('Phantom wallet detected');
          
          // Request connection
          const response = await solana.connect();
          const walletAddress = response.publicKey.toString();
          
          console.log('Connected to wallet:', walletAddress);
          onConnect?.(walletAddress);
          
        } else {
          setError('Please install Phantom wallet');
        }
      } else {
        setError('No Solana wallet detected. Please install Phantom wallet.');
      }
    } catch (err: any) {
      console.error('Wallet connection error:', err);
      if (err.code === 4001) {
        setError('Connection rejected by user');
      } else {
        setError('Failed to connect wallet');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-2">
      <button
        type="button"
        onClick={connectWallet}
        disabled={loading || disabled}
        className="w-full bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-400 hover:to-purple-500 text-white font-semibold py-2 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 text-sm"
      >
        {loading ? (
          <div className="w-3.5 h-3.5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
        ) : (
          <>
            <Wallet className="w-4 h-4" />
            <span>Connect Solana Wallet</span>
          </>
        )}
      </button>
      
      {error && (
        <p className="text-red-300 text-xs text-center">{error}</p>
      )}
      
      <p className="text-blue-200 text-xs text-center">
        Supports Phantom, Solflare, and other Solana wallets
      </p>
    </div>
  );
};
