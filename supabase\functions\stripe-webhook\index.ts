import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import Stripe from 'https://esm.sh/stripe@14.21.0'

const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
  apiVersion: '2023-10-16',
})

const supabase = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
)

serve(async (req) => {
  const signature = req.headers.get('stripe-signature')
  const body = await req.text()
  const webhookSecret = Deno.env.get('STRIPE_WEBHOOK_SECRET')

  if (!signature || !webhookSecret) {
    return new Response('Missing signature or webhook secret', { status: 400 })
  }

  try {
    // Verify webhook signature
    const event = stripe.webhooks.constructEvent(body, signature, webhookSecret)
    
    // Log webhook event
    await supabase.from('payment_webhooks').insert({
      provider: 'stripe',
      event_type: event.type,
      event_id: event.id,
      payload: event,
      signature,
      status: 'pending'
    })

    // Process webhook event
    switch (event.type) {
      case 'customer.subscription.created':
        await handleSubscriptionCreated(event.data.object as Stripe.Subscription)
        break
      
      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object as Stripe.Subscription)
        break
      
      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object as Stripe.Subscription)
        break
      
      case 'invoice.payment_succeeded':
        await handlePaymentSucceeded(event.data.object as Stripe.Invoice)
        break
      
      case 'invoice.payment_failed':
        await handlePaymentFailed(event.data.object as Stripe.Invoice)
        break
      
      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    // Mark webhook as processed
    await supabase
      .from('payment_webhooks')
      .update({ status: 'processed', processed_at: new Date().toISOString() })
      .eq('event_id', event.id)

    return new Response(JSON.stringify({ received: true }), {
      headers: { 'Content-Type': 'application/json' },
      status: 200,
    })
  } catch (err) {
    console.error('Webhook error:', err)
    
    // Log error
    await supabase
      .from('payment_webhooks')
      .update({ 
        status: 'failed', 
        error_message: err.message,
        processed_at: new Date().toISOString()
      })
      .eq('event_id', (err as any).event?.id)

    return new Response(`Webhook error: ${err.message}`, { status: 400 })
  }
})

async function handleSubscriptionCreated(subscription: Stripe.Subscription) {
  const customerId = subscription.customer as string
  const subscriptionId = subscription.id

  // Get customer details
  const customer = await stripe.customers.retrieve(customerId) as Stripe.Customer
  const userEmail = customer.email

  if (!userEmail) {
    throw new Error('Customer email not found')
  }

  // Find user by email
  const { data: user } = await supabase.auth.admin.getUserByEmail(userEmail)
  if (!user.user) {
    throw new Error('User not found')
  }

  // Create subscription record
  await supabase.from('user_subscriptions').insert({
    user_id: user.user.id,
    plan_id: 'default-plan-id', // You'd map this from Stripe price ID
    status: subscription.status === 'trialing' ? 'trial' : 'active',
    billing_cycle: subscription.items.data[0]?.price?.recurring?.interval === 'year' ? 'yearly' : 'monthly',
    current_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
    current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
    trial_start: subscription.trial_start ? new Date(subscription.trial_start * 1000).toISOString() : null,
    trial_end: subscription.trial_end ? new Date(subscription.trial_end * 1000).toISOString() : null,
    stripe_subscription_id: subscriptionId,
  })

  console.log('Subscription created for user:', user.user.id)
}

async function handleSubscriptionUpdated(subscription: Stripe.Subscription) {
  const subscriptionId = subscription.id

  // Update subscription record
  await supabase
    .from('user_subscriptions')
    .update({
      status: subscription.status === 'trialing' ? 'trial' : subscription.status,
      current_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
      current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
      cancel_at_period_end: subscription.cancel_at_period_end,
      canceled_at: subscription.canceled_at ? new Date(subscription.canceled_at * 1000).toISOString() : null,
    })
    .eq('stripe_subscription_id', subscriptionId)

  console.log('Subscription updated:', subscriptionId)
}

async function handleSubscriptionDeleted(subscription: Stripe.Subscription) {
  const subscriptionId = subscription.id

  // Update subscription status
  await supabase
    .from('user_subscriptions')
    .update({
      status: 'canceled',
      canceled_at: new Date().toISOString(),
    })
    .eq('stripe_subscription_id', subscriptionId)

  console.log('Subscription deleted:', subscriptionId)
}

async function handlePaymentSucceeded(invoice: Stripe.Invoice) {
  const subscriptionId = invoice.subscription as string
  const customerId = invoice.customer as string

  // Get subscription
  const { data: subscription } = await supabase
    .from('user_subscriptions')
    .select('*')
    .eq('stripe_subscription_id', subscriptionId)
    .single()

  if (!subscription) {
    throw new Error('Subscription not found')
  }

  // Create payment transaction record
  await supabase.from('payment_transactions').insert({
    user_id: subscription.user_id,
    subscription_id: subscription.id,
    amount: invoice.amount_paid / 100, // Convert from cents
    currency: invoice.currency.toUpperCase(),
    type: 'subscription',
    status: 'completed',
    description: `Payment for ${subscription.billing_cycle} subscription`,
    provider: 'stripe',
    provider_transaction_id: invoice.id,
    processed_at: new Date().toISOString(),
  })

  // Create billing history record
  await supabase.from('billing_history').insert({
    user_id: subscription.user_id,
    subscription_id: subscription.id,
    invoice_number: invoice.number,
    amount: invoice.amount_paid / 100,
    currency: invoice.currency.toUpperCase(),
    tax_amount: (invoice.tax || 0) / 100,
    period_start: new Date(invoice.period_start * 1000).toISOString(),
    period_end: new Date(invoice.period_end * 1000).toISOString(),
    status: 'paid',
    paid_at: new Date().toISOString(),
    provider: 'stripe',
    provider_invoice_id: invoice.id,
    invoice_pdf_url: invoice.invoice_pdf,
  })

  console.log('Payment succeeded for subscription:', subscriptionId)
}

async function handlePaymentFailed(invoice: Stripe.Invoice) {
  const subscriptionId = invoice.subscription as string

  // Get subscription
  const { data: subscription } = await supabase
    .from('user_subscriptions')
    .select('*')
    .eq('stripe_subscription_id', subscriptionId)
    .single()

  if (!subscription) {
    throw new Error('Subscription not found')
  }

  // Create failed payment transaction record
  await supabase.from('payment_transactions').insert({
    user_id: subscription.user_id,
    subscription_id: subscription.id,
    amount: invoice.amount_due / 100,
    currency: invoice.currency.toUpperCase(),
    type: 'subscription',
    status: 'failed',
    description: `Failed payment for ${subscription.billing_cycle} subscription`,
    provider: 'stripe',
    provider_transaction_id: invoice.id,
    failed_at: new Date().toISOString(),
  })

  // Update subscription status if needed
  if (invoice.attempt_count >= 3) {
    await supabase
      .from('user_subscriptions')
      .update({ status: 'past_due' })
      .eq('stripe_subscription_id', subscriptionId)
  }

  console.log('Payment failed for subscription:', subscriptionId)
}
