/**
 * KOI APP PAYMENT SERVICE
 * Unified payment processing for Stripe, PayPal, and Solana
 */

import { supabase } from '../supabase/client';

// =====================================================
// TYPES AND INTERFACES
// =====================================================

export interface PaymentMethod {
  id: string;
  type: 'stripe_card' | 'paypal' | 'solana_wallet' | 'direct_card';
  provider: 'stripe' | 'paypal' | 'solana' | 'direct';
  isDefault: boolean;
  cardLastFour?: string;
  cardBrand?: string;
  cardExpMonth?: number;
  cardExpYear?: number;
  solanaWalletAddress?: string;
}

export interface Subscription {
  id: string;
  userId: string;
  planId: string;
  status: 'trial' | 'active' | 'past_due' | 'canceled' | 'expired';
  billingCycle: 'monthly' | 'yearly';
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  trialEnd?: Date;
  cancelAtPeriodEnd: boolean;
  plan: {
    name: string;
    priceMonthly: number;
    priceYearly?: number;
    features: string[];
  };
}

export interface PaymentTransaction {
  id: string;
  amount: number;
  currency: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  provider: string;
  description: string;
  createdAt: Date;
}

export interface CreateSubscriptionParams {
  planId: string;
  paymentMethodId: string;
  billingCycle: 'monthly' | 'yearly';
  trialDays?: number;
}

export interface PaymentResult {
  success: boolean;
  subscriptionId?: string;
  clientSecret?: string;
  redirectUrl?: string;
  error?: string;
}

// =====================================================
// PAYMENT SERVICE CLASS
// =====================================================

export class PaymentService {
  private static instance: PaymentService;
  
  public static getInstance(): PaymentService {
    if (!PaymentService.instance) {
      PaymentService.instance = new PaymentService();
    }
    return PaymentService.instance;
  }

  // =====================================================
  // SUBSCRIPTION MANAGEMENT
  // =====================================================

  async getUserSubscription(userId: string): Promise<Subscription | null> {
    try {
      const { data, error } = await supabase
        .from('user_subscriptions')
        .select(`
          *,
          subscription_plans (
            name,
            price_monthly,
            price_yearly,
            features
          )
        `)
        .eq('user_id', userId)
        .single();

      if (error || !data) return null;

      return {
        id: data.id,
        userId: data.user_id,
        planId: data.plan_id,
        status: data.status,
        billingCycle: data.billing_cycle,
        currentPeriodStart: new Date(data.current_period_start),
        currentPeriodEnd: new Date(data.current_period_end),
        trialEnd: data.trial_end ? new Date(data.trial_end) : undefined,
        cancelAtPeriodEnd: data.cancel_at_period_end,
        plan: {
          name: data.subscription_plans.name,
          priceMonthly: data.subscription_plans.price_monthly,
          priceYearly: data.subscription_plans.price_yearly,
          features: data.subscription_plans.features,
        },
      };
    } catch (error) {
      console.error('Error fetching user subscription:', error);
      return null;
    }
  }

  async createSubscription(
    userId: string,
    params: CreateSubscriptionParams
  ): Promise<PaymentResult> {
    try {
      // Get payment method details
      const { data: paymentMethod } = await supabase
        .from('payment_methods')
        .select('*')
        .eq('id', params.paymentMethodId)
        .eq('user_id', userId)
        .single();

      if (!paymentMethod) {
        return { success: false, error: 'Payment method not found' };
      }

      // Route to appropriate payment provider
      switch (paymentMethod.provider) {
        case 'stripe':
          return await this.createStripeSubscription(userId, params, paymentMethod);
        case 'paypal':
          return await this.createPayPalSubscription(userId, params, paymentMethod);
        case 'solana':
          return await this.createSolanaSubscription(userId, params, paymentMethod);
        default:
          return { success: false, error: 'Unsupported payment provider' };
      }
    } catch (error) {
      console.error('Error creating subscription:', error);
      return { success: false, error: 'Failed to create subscription' };
    }
  }

  async cancelSubscription(userId: string, immediate = false): Promise<boolean> {
    try {
      const subscription = await this.getUserSubscription(userId);
      if (!subscription) return false;

      // Update local database
      const { error } = await supabase
        .from('user_subscriptions')
        .update({
          cancel_at_period_end: !immediate,
          canceled_at: immediate ? new Date().toISOString() : null,
          status: immediate ? 'canceled' : subscription.status,
        })
        .eq('user_id', userId);

      if (error) throw error;

      // Cancel with payment provider
      // This would be handled by webhook or direct API call
      await this.notifyPaymentProvider(subscription, 'cancel', { immediate });

      return true;
    } catch (error) {
      console.error('Error canceling subscription:', error);
      return false;
    }
  }

  // =====================================================
  // PAYMENT METHODS MANAGEMENT
  // =====================================================

  async getUserPaymentMethods(userId: string): Promise<PaymentMethod[]> {
    try {
      const { data, error } = await supabase
        .from('payment_methods')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true)
        .order('is_default', { ascending: false });

      if (error) throw error;

      return data.map(method => ({
        id: method.id,
        type: method.type,
        provider: method.provider,
        isDefault: method.is_default,
        cardLastFour: method.card_last_four,
        cardBrand: method.card_brand,
        cardExpMonth: method.card_exp_month,
        cardExpYear: method.card_exp_year,
        solanaWalletAddress: method.solana_wallet_address,
      }));
    } catch (error) {
      console.error('Error fetching payment methods:', error);
      return [];
    }
  }

  async addPaymentMethod(
    userId: string,
    type: PaymentMethod['type'],
    providerData: any
  ): Promise<string | null> {
    try {
      const { data, error } = await supabase
        .from('payment_methods')
        .insert({
          user_id: userId,
          type,
          provider: type.split('_')[0],
          ...providerData,
          is_default: false,
          is_active: true,
        })
        .select('id')
        .single();

      if (error) throw error;
      return data.id;
    } catch (error) {
      console.error('Error adding payment method:', error);
      return null;
    }
  }

  async setDefaultPaymentMethod(userId: string, paymentMethodId: string): Promise<boolean> {
    try {
      // Remove default from all methods
      await supabase
        .from('payment_methods')
        .update({ is_default: false })
        .eq('user_id', userId);

      // Set new default
      const { error } = await supabase
        .from('payment_methods')
        .update({ is_default: true })
        .eq('id', paymentMethodId)
        .eq('user_id', userId);

      return !error;
    } catch (error) {
      console.error('Error setting default payment method:', error);
      return false;
    }
  }

  // =====================================================
  // BILLING HISTORY
  // =====================================================

  async getBillingHistory(userId: string): Promise<PaymentTransaction[]> {
    try {
      const { data, error } = await supabase
        .from('payment_transactions')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;

      return data.map(transaction => ({
        id: transaction.id,
        amount: transaction.amount,
        currency: transaction.currency,
        status: transaction.status,
        provider: transaction.provider,
        description: transaction.description,
        createdAt: new Date(transaction.created_at),
      }));
    } catch (error) {
      console.error('Error fetching billing history:', error);
      return [];
    }
  }

  // =====================================================
  // SUBSCRIPTION STATUS CHECKS
  // =====================================================

  async hasActiveSubscription(userId: string): Promise<boolean> {
    try {
      const { data } = await supabase.rpc('user_has_active_subscription', {
        user_uuid: userId,
      });
      return data || false;
    } catch (error) {
      console.error('Error checking subscription status:', error);
      return false;
    }
  }

  async canAccessFeature(userId: string, feature: string): Promise<boolean> {
    try {
      const { data } = await supabase.rpc('user_can_access_feature', {
        user_uuid: userId,
        feature_name: feature,
      });
      return data || false;
    } catch (error) {
      console.error('Error checking feature access:', error);
      return false;
    }
  }

  // =====================================================
  // PRIVATE HELPER METHODS
  // =====================================================

  private async createStripeSubscription(
    userId: string,
    params: CreateSubscriptionParams,
    paymentMethod: any
  ): Promise<PaymentResult> {
    try {
      // Call Supabase Edge Function for Stripe integration
      const { data, error } = await supabase.functions.invoke('stripe-create-subscription', {
        body: {
          userId,
          planId: params.planId,
          paymentMethodId: params.paymentMethodId,
          billingCycle: params.billingCycle,
          trialDays: params.trialDays,
        },
      });

      if (error) throw error;

      return {
        success: true,
        subscriptionId: data.subscriptionId,
        clientSecret: data.clientSecret,
      };
    } catch (error) {
      console.error('Stripe subscription creation failed:', error);
      return {
        success: false,
        error: 'Failed to create Stripe subscription',
      };
    }
  }

  private async createPayPalSubscription(
    userId: string,
    params: CreateSubscriptionParams,
    paymentMethod: any
  ): Promise<PaymentResult> {
    try {
      // Call Supabase Edge Function for PayPal integration
      const { data, error } = await supabase.functions.invoke('paypal-create-subscription', {
        body: {
          userId,
          planId: params.planId,
          billingCycle: params.billingCycle,
          trialDays: params.trialDays,
        },
      });

      if (error) throw error;

      return {
        success: true,
        subscriptionId: data.subscriptionId,
        redirectUrl: data.approvalUrl,
      };
    } catch (error) {
      console.error('PayPal subscription creation failed:', error);
      return {
        success: false,
        error: 'Failed to create PayPal subscription',
      };
    }
  }

  private async createSolanaSubscription(
    userId: string,
    params: CreateSubscriptionParams,
    paymentMethod: any
  ): Promise<PaymentResult> {
    try {
      // Call Supabase Edge Function for Solana integration
      const { data, error } = await supabase.functions.invoke('solana-create-subscription', {
        body: {
          userId,
          planId: params.planId,
          walletAddress: paymentMethod.solana_wallet_address,
          billingCycle: params.billingCycle,
          trialDays: params.trialDays,
        },
      });

      if (error) throw error;

      return {
        success: true,
        subscriptionId: data.subscriptionId,
        redirectUrl: data.paymentUrl,
      };
    } catch (error) {
      console.error('Solana subscription creation failed:', error);
      return {
        success: false,
        error: 'Failed to create Solana subscription',
      };
    }
  }

  private async notifyPaymentProvider(
    subscription: Subscription,
    action: string,
    data: any
  ): Promise<void> {
    // This would notify the appropriate payment provider
    console.log('Notifying payment provider:', { subscription, action, data });
  }
}

// Export singleton instance
export const paymentService = PaymentService.getInstance();
