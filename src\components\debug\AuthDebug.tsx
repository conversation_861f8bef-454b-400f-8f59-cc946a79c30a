'use client';

import { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase/client';

export default function AuthDebug() {
  const [mounted, setMounted] = useState(false);
  const [debugInfo, setDebugInfo] = useState<any>({});
  const [testEmail, setTestEmail] = useState('<EMAIL>');
  const [testPassword, setTestPassword] = useState('testpassword123');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  // Handle client-side mounting
  useEffect(() => {
    console.log('AuthDebug component mounting...');
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted) return;

    const getDebugInfo = async () => {
      try {
        // Get current session
        const { data: sessionData, error: sessionError } = await supabase.auth.getSession();

        // Get current user
        const { data: userData, error: userError } = await supabase.auth.getUser();

        setDebugInfo({
          supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL,
          hasAnonKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
          currentUrl: window.location.href,
          session: sessionData.session,
          sessionError,
          user: userData.user,
          userError,
          timestamp: new Date().toISOString(),
          mounted: true
        });
      } catch (error: any) {
        console.error('Debug info error:', error);
        setDebugInfo({ error: error.message, mounted: true });
      }
    };

    getDebugInfo();
  }, [mounted]);

  const testSignUp = async () => {
    setLoading(true);
    setMessage('');

    try {
      console.log('Testing sign up with:', testEmail);

      const { data, error } = await supabase.auth.signUp({
        email: testEmail,
        password: testPassword,
        options: {
          emailRedirectTo: `${window.location.origin}/auth/callback`
        }
      });

      if (error) {
        setMessage(`❌ Sign Up Error: ${error.message}`);
        console.error('Sign up error:', error);
      } else {
        const userId = data.user?.id;
        const needsConfirmation = !data.session && data.user && !data.user.email_confirmed_at;

        if (needsConfirmation) {
          setMessage(`✅ Sign Up Success! User ID: ${userId}. ⚠️ Email confirmation required - check your email!`);
        } else if (data.session) {
          setMessage(`✅ Sign Up Success! User ID: ${userId}. You are now signed in!`);
        } else {
          setMessage(`✅ Sign Up Success! User ID: ${userId}`);
        }
        console.log('Sign up success:', data);
      }
    } catch (error: any) {
      setMessage(`❌ Exception: ${error.message}`);
      console.error('Sign up exception:', error);
    } finally {
      setLoading(false);
    }
  };

  const testSignIn = async () => {
    setLoading(true);
    setMessage('');

    try {
      console.log('Testing sign in with:', testEmail);

      const { data, error } = await supabase.auth.signInWithPassword({
        email: testEmail,
        password: testPassword
      });

      if (error) {
        let errorMsg = error.message;
        if (errorMsg.includes('Invalid login credentials')) {
          errorMsg = 'Invalid email/password combination. Make sure you signed up first and confirmed your email.';
        } else if (errorMsg.includes('Email not confirmed')) {
          errorMsg = 'Please check your email and click the verification link before signing in.';
        }
        setMessage(`❌ Sign In Error: ${errorMsg}`);
        console.error('Sign in error:', error);
      } else {
        setMessage(`✅ Sign In Success! Welcome ${data.user?.email}!`);
        console.log('Sign in success:', data);
      }
    } catch (error: any) {
      setMessage(`❌ Exception: ${error.message}`);
      console.error('Sign in exception:', error);
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    setLoading(true);
    try {
      await supabase.auth.signOut();
      setMessage('✅ Signed out successfully');
      // Refresh debug info
      window.location.reload();
    } catch (error: any) {
      setMessage(`❌ Sign out error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Don't render until mounted to avoid hydration issues
  if (!mounted) {
    return (
      <div className="max-w-4xl mx-auto p-6 bg-gray-100 min-h-screen">
        <h1 className="text-3xl font-bold mb-6 text-gray-800">🔧 Auth Debug Panel</h1>
        <div className="bg-white rounded-lg shadow-md p-6">
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6 bg-gray-100 min-h-screen">
      <h1 className="text-3xl font-bold mb-6 text-gray-800">🔧 Auth Debug Panel</h1>
      
      {/* Debug Information */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4 text-gray-700">Debug Information</h2>
        <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-96">
          {JSON.stringify(debugInfo, null, 2)}
        </pre>
      </div>

      {/* Test Controls */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4 text-gray-700">Test Authentication</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label htmlFor="test-email" className="block text-sm font-medium text-gray-700 mb-2">
              Test Email:
            </label>
            <input
              id="test-email"
              type="email"
              value={testEmail}
              onChange={(e) => {
                console.log('Email input changed:', e.target.value);
                setTestEmail(e.target.value);
              }}
              placeholder="Enter test email"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white"
            />
          </div>

          <div>
            <label htmlFor="test-password" className="block text-sm font-medium text-gray-700 mb-2">
              Test Password:
            </label>
            <input
              id="test-password"
              type="password"
              value={testPassword}
              onChange={(e) => {
                console.log('Password input changed:', e.target.value);
                setTestPassword(e.target.value);
              }}
              placeholder="Enter test password"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white"
            />
          </div>
        </div>

        <div className="flex flex-wrap gap-3 mb-4">
          <button
            type="button"
            onClick={() => {
              console.log('Test button clicked!');
              alert(`Current values: Email: ${testEmail}, Password: ${testPassword}`);
            }}
            className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
          >
            Test Inputs
          </button>

          <button
            type="button"
            onClick={testSignUp}
            disabled={loading}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
          >
            {loading ? 'Testing...' : 'Test Sign Up'}
          </button>

          <button
            type="button"
            onClick={testSignIn}
            disabled={loading}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Testing...' : 'Test Sign In'}
          </button>

          <button
            type="button"
            onClick={signOut}
            disabled={loading}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
          >
            {loading ? 'Signing Out...' : 'Sign Out'}
          </button>
        </div>

        {message && (
          <div className={`p-3 rounded-md ${message.includes('❌') ? 'bg-red-100 border border-red-300' : 'bg-green-100 border border-green-300'}`}>
            <p className="text-sm text-gray-800">{message}</p>
          </div>
        )}

        {/* Current Auth Status */}
        <div className="mt-4 p-3 bg-blue-100 border border-blue-300 rounded-md">
          <h3 className="font-semibold text-blue-800 mb-2">Current Auth Status:</h3>
          <p className="text-sm text-blue-700">
            {debugInfo.session ?
              `✅ Signed in as: ${debugInfo.session.user?.email}` :
              '❌ Not signed in'
            }
          </p>
          {debugInfo.session && (
            <p className="text-xs text-blue-600 mt-1">
              Session expires: {new Date(debugInfo.session.expires_at * 1000).toLocaleString()}
            </p>
          )}
        </div>
      </div>

      {/* Instructions */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4 text-yellow-800">Instructions</h2>
        <ol className="list-decimal list-inside space-y-2 text-yellow-700">
          <li>Check the debug information above for current auth state</li>
          <li>Try the "Test Sign Up" button with a new email</li>
          <li>Check browser console for detailed logs</li>
          <li>If sign up fails, check Supabase dashboard settings</li>
          <li>Verify redirect URLs are configured correctly</li>
        </ol>
      </div>
    </div>
  );
}
