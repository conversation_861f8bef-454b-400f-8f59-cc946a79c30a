# 🔧 Supabase Authentication Fix Guide

## Issue Description
Users are getting "access_denied" with "otp_expired" error when trying to sign up. This indicates an issue with email verification configuration.

## Root Cause Analysis
The error occurs because:
1. Email confirmation is required but not properly configured
2. Redirect URLs may not be set correctly in Supabase
3. Email delivery might be delayed or failing

## 🚨 IMMEDIATE FIXES NEEDED

### 1. Configure Supabase Authentication Settings

**Go to your Supabase Dashboard:**
1. Navigate to: https://app.supabase.com/project/arkywiwduoqpobflwssm
2. Go to **Authentication** → **Settings**

### 2. Update Site URL and Redirect URLs

**Site URL:**
- Set to: `http://localhost:3000` (for development)
- For production: `https://your-domain.com`

**Redirect URLs (Add these):**
- `http://localhost:3000/auth/callback`
- `https://your-domain.com/auth/callback` (for production)

### 3. Email Confirmation Settings

**Option A: Disable Email Confirmation (Quick Fix for Testing)**
1. Go to **Authentication** → **Settings**
2. Find "Enable email confirmations"
3. **UNCHECK** this option temporarily
4. Save changes

**Option B: Configure Email Properly (Recommended for Production)**
1. Keep "Enable email confirmations" **CHECKED**
2. Configure custom SMTP settings (recommended)
3. Test email delivery

### 4. Email Template Configuration

1. Go to **Authentication** → **Email Templates**
2. Customize the "Confirm signup" template
3. Ensure the redirect URL is correct: `{{ .SiteURL }}/auth/callback`

## 🧪 Testing the Fix

### Test Sign Up Process:
1. Start your development server: `npm run dev`
2. Go to: http://localhost:3000/auth
3. Try signing up with a new email
4. Check browser console for detailed logs
5. Check your email for verification link

### Expected Behavior:
- **With email confirmation disabled:** User should be signed in immediately
- **With email confirmation enabled:** User should receive email and be able to verify

## 🔍 Debugging Steps

### Check Browser Console:
1. Open Developer Tools (F12)
2. Go to Console tab
3. Look for authentication logs
4. Check for any error messages

### Check Network Tab:
1. Go to Network tab in Developer Tools
2. Filter by "supabase"
3. Look for failed requests
4. Check response details

## 📧 Email Configuration (Production)

For production, configure custom SMTP:

1. Go to **Authentication** → **Settings** → **SMTP Settings**
2. Configure your email provider (SendGrid, Mailgun, etc.)
3. Test email delivery

## 🔐 Security Considerations

- **Never disable email confirmation in production**
- Always use HTTPS in production
- Configure proper CORS settings
- Use environment variables for sensitive data

## 📝 Code Changes Made

1. **Enhanced auth callback handling** (`src/app/auth/callback/page.tsx`)
   - Added support for both PKCE and legacy flows
   - Better error handling and user-friendly messages
   - Comprehensive logging for debugging

2. **Improved sign-up process** (`src/lib/supabase/client.ts`)
   - Added detailed logging
   - Better error tracking

3. **Enhanced error messages** (`src/components/pages/Auth.tsx`)
   - User-friendly error messages
   - Better success feedback

## 🚀 Next Steps

1. **Apply Supabase configuration changes**
2. **Test the sign-up process**
3. **Verify email delivery**
4. **Configure production settings**

## 📞 Support

If issues persist:
1. Check Supabase logs in dashboard
2. Verify environment variables
3. Test with different email providers
4. Contact Supabase support if needed
