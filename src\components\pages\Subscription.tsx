'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { paymentService, Subscription, PaymentTransaction } from '../../lib/payments/paymentService';
import { useTranslation } from '../../lib/i18n/useTranslation';
import { SubscriptionCard } from '../payments/SubscriptionCard';
import { PaymentForm } from '../payments/PaymentForm';
import { 
  ArrowLeft,
  Download,
  CreditCard,
  Calendar,
  DollarSign,
  TrendingUp,
  Settings,
  Crown
} from 'lucide-react';

interface SubscriptionPageProps {
  onNavigate?: (page: string) => void;
}

export const SubscriptionPage = ({ onNavigate }: SubscriptionPageProps) => {
  const { user } = useAuth();
  const { t } = useTranslation();
  
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [billingHistory, setBillingHistory] = useState<PaymentTransaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [showPaymentForm, setShowPaymentForm] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'billing' | 'upgrade'>('overview');

  useEffect(() => {
    if (user?.id) {
      loadSubscriptionData();
    }
  }, [user?.id]);

  const loadSubscriptionData = async () => {
    if (!user?.id) return;
    
    try {
      setLoading(true);
      const [sub, history] = await Promise.all([
        paymentService.getUserSubscription(user.id),
        paymentService.getBillingHistory(user.id),
      ]);
      
      setSubscription(sub);
      setBillingHistory(history);
      
      // Show upgrade form if no subscription
      if (!sub) {
        setActiveTab('upgrade');
      }
    } catch (error) {
      console.error('Error loading subscription data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpgradeSuccess = (subscriptionId: string) => {
    setShowPaymentForm(false);
    setActiveTab('overview');
    loadSubscriptionData();
  };

  const formatCurrency = (amount: number, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
    }).format(amount);
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(date);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 p-4">
        <div className="max-w-4xl mx-auto">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-blue-400/30 rounded w-1/3"></div>
            <div className="h-64 bg-blue-400/20 rounded-xl"></div>
            <div className="h-32 bg-blue-400/20 rounded-xl"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => onNavigate?.('dashboard')}
              className="p-2 rounded-lg bg-blue-600/50 hover:bg-blue-600/70 text-white transition-colors duration-200"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <div>
              <h1 className="text-2xl font-bold text-white">
                {t('subscription.title')}
              </h1>
              <p className="text-blue-200">
                {t('subscription.subtitle')}
              </p>
            </div>
          </div>
          <Crown className="w-8 h-8 text-yellow-400" />
        </div>

        {/* Navigation Tabs */}
        <div className="flex space-x-1 mb-6 bg-blue-600/30 rounded-lg p-1">
          {[
            { id: 'overview', label: 'Overview', icon: TrendingUp },
            { id: 'billing', label: 'Billing History', icon: Calendar },
            { id: 'upgrade', label: 'Upgrade', icon: Crown },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md transition-all duration-200 ${
                activeTab === tab.id
                  ? 'bg-blue-500 text-white shadow-lg'
                  : 'text-blue-200 hover:text-white hover:bg-blue-600/50'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              <span className="font-medium">{tab.label}</span>
            </button>
          ))}
        </div>

        {/* Content */}
        <div className="space-y-6">
          {/* Overview Tab */}
          {activeTab === 'overview' && (
            <div className="space-y-6">
              <SubscriptionCard
                onManagePayment={() => setActiveTab('billing')}
                onUpgrade={() => setActiveTab('upgrade')}
              />

              {subscription && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {/* Next Billing */}
                  <div className="bg-gradient-to-br from-green-600/20 to-green-700/20 rounded-xl p-4 border border-green-400/30">
                    <div className="flex items-center space-x-3 mb-2">
                      <Calendar className="w-5 h-5 text-green-400" />
                      <h3 className="font-medium text-white">Next Billing</h3>
                    </div>
                    <p className="text-green-200 text-sm">
                      {formatDate(subscription.currentPeriodEnd)}
                    </p>
                    <p className="text-green-100 font-semibold">
                      {formatCurrency(
                        subscription.billingCycle === 'yearly'
                          ? subscription.plan.priceYearly || subscription.plan.priceMonthly * 12
                          : subscription.plan.priceMonthly
                      )}
                    </p>
                  </div>

                  {/* Payment Method */}
                  <div className="bg-gradient-to-br from-purple-600/20 to-purple-700/20 rounded-xl p-4 border border-purple-400/30">
                    <div className="flex items-center space-x-3 mb-2">
                      <CreditCard className="w-5 h-5 text-purple-400" />
                      <h3 className="font-medium text-white">Payment Method</h3>
                    </div>
                    <p className="text-purple-200 text-sm">
                      •••• •••• •••• 4242
                    </p>
                    <p className="text-purple-100 font-semibold">
                      Visa
                    </p>
                  </div>

                  {/* Total Spent */}
                  <div className="bg-gradient-to-br from-yellow-600/20 to-yellow-700/20 rounded-xl p-4 border border-yellow-400/30">
                    <div className="flex items-center space-x-3 mb-2">
                      <DollarSign className="w-5 h-5 text-yellow-400" />
                      <h3 className="font-medium text-white">Total Spent</h3>
                    </div>
                    <p className="text-yellow-200 text-sm">
                      This year
                    </p>
                    <p className="text-yellow-100 font-semibold">
                      {formatCurrency(
                        billingHistory
                          .filter(t => t.status === 'completed')
                          .reduce((sum, t) => sum + t.amount, 0)
                      )}
                    </p>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Billing History Tab */}
          {activeTab === 'billing' && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-white">
                  Billing History
                </h2>
                <button className="flex items-center space-x-2 bg-blue-600/50 hover:bg-blue-600/70 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                  <Download className="w-4 h-4" />
                  <span>Export</span>
                </button>
              </div>

              <div className="bg-gradient-to-br from-blue-600/20 to-purple-600/20 rounded-xl border border-blue-400/30 overflow-hidden">
                {billingHistory.length > 0 ? (
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead className="bg-blue-600/30">
                        <tr>
                          <th className="text-left py-3 px-4 text-blue-200 font-medium">Date</th>
                          <th className="text-left py-3 px-4 text-blue-200 font-medium">Description</th>
                          <th className="text-left py-3 px-4 text-blue-200 font-medium">Amount</th>
                          <th className="text-left py-3 px-4 text-blue-200 font-medium">Status</th>
                          <th className="text-left py-3 px-4 text-blue-200 font-medium">Method</th>
                        </tr>
                      </thead>
                      <tbody>
                        {billingHistory.map((transaction) => (
                          <tr key={transaction.id} className="border-t border-blue-400/20">
                            <td className="py-3 px-4 text-white">
                              {formatDate(transaction.createdAt)}
                            </td>
                            <td className="py-3 px-4 text-white">
                              {transaction.description}
                            </td>
                            <td className="py-3 px-4 text-white font-medium">
                              {formatCurrency(transaction.amount, transaction.currency)}
                            </td>
                            <td className="py-3 px-4">
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                transaction.status === 'completed'
                                  ? 'bg-green-500/20 text-green-200'
                                  : transaction.status === 'failed'
                                  ? 'bg-red-500/20 text-red-200'
                                  : 'bg-yellow-500/20 text-yellow-200'
                              }`}>
                                {transaction.status}
                              </span>
                            </td>
                            <td className="py-3 px-4 text-blue-200 capitalize">
                              {transaction.provider}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div className="p-8 text-center">
                    <Calendar className="w-12 h-12 text-blue-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-white mb-2">
                      No billing history
                    </h3>
                    <p className="text-blue-200">
                      Your billing history will appear here once you have transactions.
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Upgrade Tab */}
          {activeTab === 'upgrade' && (
            <div className="space-y-6">
              <div className="text-center mb-8">
                <Crown className="w-16 h-16 text-yellow-400 mx-auto mb-4" />
                <h2 className="text-2xl font-bold text-white mb-2">
                  Upgrade to Koi Premium
                </h2>
                <p className="text-blue-200 max-w-2xl mx-auto">
                  Unlock all productivity and gaming features with unlimited access to notes, tasks, AI assistant, and more.
                </p>
              </div>

              <PaymentForm
                planId="default-plan-id"
                billingCycle="monthly"
                onSuccess={handleUpgradeSuccess}
                onError={(error) => console.error('Payment error:', error)}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
