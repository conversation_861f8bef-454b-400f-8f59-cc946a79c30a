'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { paymentService, Subscription } from '../../lib/payments/paymentService';
import { useTranslation } from '../../lib/i18n/useTranslation';
import { 
  CreditCard, 
  Calendar, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Crown,
  Zap
} from 'lucide-react';

interface SubscriptionCardProps {
  onManagePayment?: () => void;
  onUpgrade?: () => void;
}

export const SubscriptionCard = ({ onManagePayment, onUpgrade }: SubscriptionCardProps) => {
  const { user } = useAuth();
  const { t } = useTranslation();
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user?.id) {
      loadSubscription();
    }
  }, [user?.id]);

  const loadSubscription = async () => {
    if (!user?.id) return;
    
    try {
      setLoading(true);
      const sub = await paymentService.getUserSubscription(user.id);
      setSubscription(sub);
    } catch (error) {
      console.error('Error loading subscription:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'text-green-400 bg-green-500/20 border-green-400/30';
      case 'trial':
        return 'text-blue-400 bg-blue-500/20 border-blue-400/30';
      case 'past_due':
        return 'text-yellow-400 bg-yellow-500/20 border-yellow-400/30';
      case 'canceled':
      case 'expired':
        return 'text-red-400 bg-red-500/20 border-red-400/30';
      default:
        return 'text-gray-400 bg-gray-500/20 border-gray-400/30';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="w-4 h-4" />;
      case 'trial':
        return <Zap className="w-4 h-4" />;
      case 'past_due':
        return <AlertTriangle className="w-4 h-4" />;
      case 'canceled':
      case 'expired':
        return <XCircle className="w-4 h-4" />;
      default:
        return <AlertTriangle className="w-4 h-4" />;
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(date);
  };

  const getDaysRemaining = (endDate: Date) => {
    const now = new Date();
    const diffTime = endDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  };

  if (loading) {
    return (
      <div className="bg-gradient-to-br from-blue-600/20 to-purple-600/20 rounded-xl p-6 border border-blue-400/30">
        <div className="animate-pulse">
          <div className="h-6 bg-blue-400/30 rounded mb-4"></div>
          <div className="h-4 bg-blue-400/20 rounded mb-2"></div>
          <div className="h-4 bg-blue-400/20 rounded w-3/4"></div>
        </div>
      </div>
    );
  }

  if (!subscription) {
    return (
      <div className="bg-gradient-to-br from-blue-600/20 to-purple-600/20 rounded-xl p-6 border border-blue-400/30">
        <div className="text-center">
          <Crown className="w-12 h-12 text-yellow-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-white mb-2">
            {t('subscription.noSubscription')}
          </h3>
          <p className="text-blue-200 mb-4">
            {t('subscription.upgradeMessage')}
          </p>
          <button
            onClick={onUpgrade}
            className="bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-400 hover:to-orange-400 text-white font-semibold py-2 px-6 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
          >
            {t('subscription.upgradeToPremium')}
          </button>
        </div>
      </div>
    );
  }

  const daysRemaining = subscription.trialEnd 
    ? getDaysRemaining(subscription.trialEnd)
    : getDaysRemaining(subscription.currentPeriodEnd);

  return (
    <div className="bg-gradient-to-br from-blue-600/20 to-purple-600/20 rounded-xl p-6 border border-blue-400/30">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <Crown className="w-6 h-6 text-yellow-400" />
          <h3 className="text-lg font-semibold text-white">
            {subscription.plan.name}
          </h3>
        </div>
        <div className={`flex items-center space-x-2 px-3 py-1 rounded-full border text-sm font-medium ${getStatusColor(subscription.status)}`}>
          {getStatusIcon(subscription.status)}
          <span className="capitalize">{subscription.status}</span>
        </div>
      </div>

      {/* Subscription Details */}
      <div className="space-y-3 mb-6">
        <div className="flex items-center justify-between text-sm">
          <span className="text-blue-200">Billing Cycle:</span>
          <span className="text-white font-medium capitalize">
            {subscription.billingCycle}
          </span>
        </div>

        <div className="flex items-center justify-between text-sm">
          <span className="text-blue-200">Amount:</span>
          <span className="text-white font-medium">
            ${subscription.billingCycle === 'yearly' 
              ? subscription.plan.priceYearly || subscription.plan.priceMonthly * 12
              : subscription.plan.priceMonthly
            }/{subscription.billingCycle === 'yearly' ? 'year' : 'month'}
          </span>
        </div>

        {subscription.status === 'trial' && subscription.trialEnd && (
          <div className="flex items-center justify-between text-sm">
            <span className="text-blue-200">Trial Ends:</span>
            <span className="text-white font-medium">
              {formatDate(subscription.trialEnd)} ({daysRemaining} days)
            </span>
          </div>
        )}

        {subscription.status === 'active' && (
          <div className="flex items-center justify-between text-sm">
            <span className="text-blue-200">Next Billing:</span>
            <span className="text-white font-medium">
              {formatDate(subscription.currentPeriodEnd)}
            </span>
          </div>
        )}

        {subscription.cancelAtPeriodEnd && (
          <div className="bg-yellow-500/20 border border-yellow-400/30 rounded-lg p-3">
            <div className="flex items-center space-x-2 text-yellow-200">
              <AlertTriangle className="w-4 h-4" />
              <span className="text-sm">
                Subscription will cancel on {formatDate(subscription.currentPeriodEnd)}
              </span>
            </div>
          </div>
        )}
      </div>

      {/* Features */}
      <div className="mb-6">
        <h4 className="text-sm font-medium text-blue-200 mb-2">Included Features:</h4>
        <div className="grid grid-cols-1 gap-1">
          {subscription.plan.features.map((feature, index) => (
            <div key={index} className="flex items-center space-x-2 text-sm text-white">
              <CheckCircle className="w-3 h-3 text-green-400 flex-shrink-0" />
              <span>{feature}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Actions */}
      <div className="flex space-x-3">
        <button
          onClick={onManagePayment}
          className="flex-1 bg-blue-600/50 hover:bg-blue-600/70 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2"
        >
          <CreditCard className="w-4 h-4" />
          <span>Manage Payment</span>
        </button>
        
        {subscription.status === 'trial' && (
          <button
            onClick={onUpgrade}
            className="flex-1 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-400 hover:to-green-500 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2"
          >
            <Crown className="w-4 h-4" />
            <span>Upgrade Now</span>
          </button>
        )}
      </div>
    </div>
  );
};
