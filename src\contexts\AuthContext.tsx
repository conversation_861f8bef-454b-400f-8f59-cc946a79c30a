'use client';

import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { auth } from '../lib/supabase/client';

interface AuthContextType {
  user: User | null;
  session: Session | null;
  loading: boolean;
  signUp: (email: string, password: string, userData?: any) => Promise<{ data: any; error: any }>;
  signIn: (email: string, password: string) => Promise<{ data: any; error: any }>;
  signInWithMagicLink: (email: string) => Promise<{ data: any; error: any }>;
  signInWithGoogle: () => Promise<{ data: any; error: any }>;
  signInWithWallet: (walletAddress: string, signature: string, message: string) => Promise<{ data: any; error: any }>;
  signOut: () => Promise<{ error: any }>;
  resetPassword: (email: string) => Promise<{ data: any; error: any }>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        if (auth?.getSession) {
          const { session } = await auth.getSession();
          setSession(session);
          setUser(session?.user ?? null);
        } else {
          console.warn('Auth not properly initialized');
          setSession(null);
          setUser(null);
        }
      } catch (error) {
        console.error('Error getting initial session:', error);
        setSession(null);
        setUser(null);
      } finally {
        setLoading(false);
      }
    };

    getInitialSession();

    // Listen for auth changes
    let subscription: any = null;
    if (auth?.onAuthStateChange) {
      const { data: { subscription: authSubscription } } = auth.onAuthStateChange(
        async (event, session) => {
          try {
            setSession(session);
            setUser(session?.user ?? null);
            setLoading(false);

            // Handle different auth events
            if (event === 'SIGNED_IN') {
              console.log('User signed in:', session?.user?.email);
            } else if (event === 'SIGNED_OUT') {
              console.log('User signed out');
            } else if (event === 'TOKEN_REFRESHED') {
              console.log('Token refreshed');
            }
          } catch (error) {
            console.error('Error in auth state change:', error);
          }
        }
      );
      subscription = authSubscription;
    }

    return () => {
      try {
        if (subscription?.unsubscribe) {
          subscription.unsubscribe();
        }
      } catch (error) {
        console.error('Error unsubscribing from auth changes:', error);
      }
    };
  }, []);

  const value: AuthContextType = {
    user,
    session,
    loading,
    signUp: auth?.signUp || (async () => ({ data: null, error: new Error('Auth not initialized') })),
    signIn: auth?.signIn || (async () => ({ data: null, error: new Error('Auth not initialized') })),
    signInWithMagicLink: auth?.signInWithMagicLink || (async () => ({ data: null, error: new Error('Auth not initialized') })),
    signInWithGoogle: auth?.signInWithGoogle || (async () => ({ data: null, error: new Error('Auth not initialized') })),
    signInWithWallet: auth?.signInWithWallet || (async () => ({ data: null, error: new Error('Auth not initialized') })),
    signOut: auth?.signOut || (async () => ({ error: new Error('Auth not initialized') })),
    resetPassword: auth?.resetPassword || (async () => ({ data: null, error: new Error('Auth not initialized') })),
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
