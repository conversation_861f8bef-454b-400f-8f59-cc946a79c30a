'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { paymentService, PaymentMethod } from '../../lib/payments/paymentService';
import { useTranslation } from '../../lib/i18n/useTranslation';
import { 
  CreditCard, 
  Wallet,
  Shield,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react';

interface PaymentFormProps {
  planId: string;
  billingCycle: 'monthly' | 'yearly';
  onSuccess?: (subscriptionId: string) => void;
  onError?: (error: string) => void;
}

export const PaymentForm = ({ planId, billingCycle, onSuccess, onError }: PaymentFormProps) => {
  const { user } = useAuth();
  const { t } = useTranslation();
  
  const [selectedMethod, setSelectedMethod] = useState<'stripe' | 'paypal' | 'solana'>('stripe');
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [loading, setLoading] = useState(false);
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState<string>('');
  const [success, setSuccess] = useState<string>('');

  // Stripe Elements (would be imported from @stripe/react-stripe-js)
  const [cardElement, setCardElement] = useState<any>(null);
  
  // Solana wallet connection
  const [solanaWallet, setSolanaWallet] = useState<any>(null);

  useEffect(() => {
    if (user?.id) {
      loadPaymentMethods();
    }
  }, [user?.id]);

  const loadPaymentMethods = async () => {
    if (!user?.id) return;
    
    try {
      setLoading(true);
      const methods = await paymentService.getUserPaymentMethods(user.id);
      setPaymentMethods(methods);
    } catch (error) {
      console.error('Error loading payment methods:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleStripePayment = async () => {
    try {
      setProcessing(true);
      setError('');

      // This would integrate with Stripe Elements
      // For now, simulate the process
      const result = await paymentService.createSubscription(user!.id, {
        planId,
        paymentMethodId: 'stripe_method_id', // Would come from Stripe Elements
        billingCycle,
        trialDays: 7,
      });

      if (result.success && result.subscriptionId) {
        setSuccess('Subscription created successfully!');
        onSuccess?.(result.subscriptionId);
      } else {
        throw new Error(result.error || 'Payment failed');
      }
    } catch (error: any) {
      const errorMessage = error.message || 'Payment processing failed';
      setError(errorMessage);
      onError?.(errorMessage);
    } finally {
      setProcessing(false);
    }
  };

  const handlePayPalPayment = async () => {
    try {
      setProcessing(true);
      setError('');

      const result = await paymentService.createSubscription(user!.id, {
        planId,
        paymentMethodId: 'paypal_method_id',
        billingCycle,
        trialDays: 7,
      });

      if (result.success && result.redirectUrl) {
        // Redirect to PayPal
        window.location.href = result.redirectUrl;
      } else {
        throw new Error(result.error || 'PayPal setup failed');
      }
    } catch (error: any) {
      const errorMessage = error.message || 'PayPal processing failed';
      setError(errorMessage);
      onError?.(errorMessage);
    } finally {
      setProcessing(false);
    }
  };

  const handleSolanaPayment = async () => {
    try {
      setProcessing(true);
      setError('');

      // Connect to Solana wallet if not connected
      if (!solanaWallet && typeof window !== 'undefined' && (window as any).solana) {
        const wallet = (window as any).solana;
        await wallet.connect();
        setSolanaWallet(wallet);
      }

      if (!solanaWallet) {
        throw new Error('Solana wallet not connected');
      }

      const result = await paymentService.createSubscription(user!.id, {
        planId,
        paymentMethodId: 'solana_wallet_id',
        billingCycle,
        trialDays: 7,
      });

      if (result.success && result.redirectUrl) {
        // Redirect to Solana payment page
        window.location.href = result.redirectUrl;
      } else {
        throw new Error(result.error || 'Solana payment setup failed');
      }
    } catch (error: any) {
      const errorMessage = error.message || 'Solana payment failed';
      setError(errorMessage);
      onError?.(errorMessage);
    } finally {
      setProcessing(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    switch (selectedMethod) {
      case 'stripe':
        await handleStripePayment();
        break;
      case 'paypal':
        await handlePayPalPayment();
        break;
      case 'solana':
        await handleSolanaPayment();
        break;
    }
  };

  return (
    <div className="bg-gradient-to-br from-blue-600/20 to-purple-600/20 rounded-xl p-6 border border-blue-400/30">
      <div className="mb-6">
        <h3 className="text-xl font-semibold text-white mb-2">
          {t('payment.choosePaymentMethod')}
        </h3>
        <p className="text-blue-200 text-sm">
          {t('payment.securePaymentMessage')}
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Payment Method Selection */}
        <div className="space-y-3">
          <label className="text-sm font-medium text-blue-200">
            {t('payment.paymentMethod')}
          </label>
          
          {/* Stripe Option */}
          <div 
            className={`p-4 rounded-lg border cursor-pointer transition-all duration-200 ${
              selectedMethod === 'stripe'
                ? 'border-blue-400 bg-blue-500/20'
                : 'border-blue-400/30 bg-blue-600/10 hover:bg-blue-600/20'
            }`}
            onClick={() => setSelectedMethod('stripe')}
          >
            <div className="flex items-center space-x-3">
              <div className={`w-4 h-4 rounded-full border-2 ${
                selectedMethod === 'stripe' 
                  ? 'border-blue-400 bg-blue-400' 
                  : 'border-blue-400/50'
              }`}>
                {selectedMethod === 'stripe' && (
                  <div className="w-full h-full rounded-full bg-white scale-50"></div>
                )}
              </div>
              <CreditCard className="w-5 h-5 text-blue-400" />
              <div>
                <div className="text-white font-medium">Credit/Debit Card</div>
                <div className="text-blue-200 text-sm">Visa, Mastercard, American Express</div>
              </div>
            </div>
          </div>

          {/* PayPal Option */}
          <div 
            className={`p-4 rounded-lg border cursor-pointer transition-all duration-200 ${
              selectedMethod === 'paypal'
                ? 'border-blue-400 bg-blue-500/20'
                : 'border-blue-400/30 bg-blue-600/10 hover:bg-blue-600/20'
            }`}
            onClick={() => setSelectedMethod('paypal')}
          >
            <div className="flex items-center space-x-3">
              <div className={`w-4 h-4 rounded-full border-2 ${
                selectedMethod === 'paypal' 
                  ? 'border-blue-400 bg-blue-400' 
                  : 'border-blue-400/50'
              }`}>
                {selectedMethod === 'paypal' && (
                  <div className="w-full h-full rounded-full bg-white scale-50"></div>
                )}
              </div>
              <div className="w-5 h-5 bg-blue-600 rounded flex items-center justify-center">
                <span className="text-white text-xs font-bold">PP</span>
              </div>
              <div>
                <div className="text-white font-medium">PayPal</div>
                <div className="text-blue-200 text-sm">PayPal balance, bank account, or card</div>
              </div>
            </div>
          </div>

          {/* Solana Option */}
          <div 
            className={`p-4 rounded-lg border cursor-pointer transition-all duration-200 ${
              selectedMethod === 'solana'
                ? 'border-blue-400 bg-blue-500/20'
                : 'border-blue-400/30 bg-blue-600/10 hover:bg-blue-600/20'
            }`}
            onClick={() => setSelectedMethod('solana')}
          >
            <div className="flex items-center space-x-3">
              <div className={`w-4 h-4 rounded-full border-2 ${
                selectedMethod === 'solana' 
                  ? 'border-blue-400 bg-blue-400' 
                  : 'border-blue-400/50'
              }`}>
                {selectedMethod === 'solana' && (
                  <div className="w-full h-full rounded-full bg-white scale-50"></div>
                )}
              </div>
              <Wallet className="w-5 h-5 text-purple-400" />
              <div>
                <div className="text-white font-medium">Solana Wallet</div>
                <div className="text-blue-200 text-sm">Pay with USDC or SOL</div>
              </div>
            </div>
          </div>
        </div>

        {/* Stripe Card Input (would be Stripe Elements) */}
        {selectedMethod === 'stripe' && (
          <div className="space-y-3">
            <label className="text-sm font-medium text-blue-200">
              Card Information
            </label>
            <div className="p-4 bg-blue-600/20 border border-blue-400/30 rounded-lg">
              <div className="text-blue-200 text-sm text-center">
                Stripe Elements would be integrated here
              </div>
            </div>
          </div>
        )}

        {/* Error/Success Messages */}
        {error && (
          <div className="flex items-center space-x-2 p-3 bg-red-500/20 border border-red-400/30 rounded-lg text-red-200">
            <AlertCircle className="w-4 h-4 flex-shrink-0" />
            <span className="text-sm">{error}</span>
          </div>
        )}

        {success && (
          <div className="flex items-center space-x-2 p-3 bg-green-500/20 border border-green-400/30 rounded-lg text-green-200">
            <CheckCircle className="w-4 h-4 flex-shrink-0" />
            <span className="text-sm">{success}</span>
          </div>
        )}

        {/* Security Notice */}
        <div className="flex items-center space-x-2 p-3 bg-blue-600/20 border border-blue-400/30 rounded-lg">
          <Shield className="w-4 h-4 text-blue-400 flex-shrink-0" />
          <span className="text-blue-200 text-sm">
            Your payment information is encrypted and secure
          </span>
        </div>

        {/* Submit Button */}
        <button
          type="submit"
          disabled={processing}
          className="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-400 hover:to-green-500 disabled:from-gray-500 disabled:to-gray-600 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:transform-none disabled:cursor-not-allowed flex items-center justify-center space-x-2"
        >
          {processing ? (
            <>
              <Loader2 className="w-4 h-4 animate-spin" />
              <span>Processing...</span>
            </>
          ) : (
            <>
              <span>Start 7-Day Free Trial</span>
            </>
          )}
        </button>

        <p className="text-blue-200 text-xs text-center">
          You won't be charged during your 7-day free trial. Cancel anytime.
        </p>
      </form>
    </div>
  );
};
