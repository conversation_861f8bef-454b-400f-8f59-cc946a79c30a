# 💳 **KOI APP PAYMENT SYSTEM SETUP GUIDE**

## 🏗️ **ARCHITECTURE OVERVIEW**

### **Payment Flow Architecture**
```
User Registration → 7-Day Free Trial → Payment Required → Full Access
                                    ↓
                            [Stripe/PayPal/Solana]
                                    ↓
                            Webhook Processing → Database Update
```

### **Provider Priority**
1. **Stripe** (Primary) - Best conversion rates, comprehensive features
2. **PayPal** (Secondary) - International trust, alternative payment methods
3. **Solana** (Crypto) - Lower fees, early adopter appeal
4. **Direct Card** (Backup) - Redundancy and control

## 🔧 **SUPABASE CONFIGURATION**

### **Required Webhook URLs**
Add these to your Supabase project settings:

```bash
# Stripe Webhooks
https://arkywiwduoqpobflwssm.supabase.co/functions/v1/stripe-webhook

# PayPal Webhooks  
https://arkywiwduoqpobflwssm.supabase.co/functions/v1/paypal-webhook

# Solana Transaction Monitor
https://arkywiwduoqpobflwssm.supabase.co/functions/v1/solana-webhook

# Payment Success/Cancel Redirects
https://arkywiwduoqpobflwssm.supabase.co/auth/v1/callback
```

### **Required Environment Variables**
Add to your Supabase Edge Functions:

```bash
# Stripe Configuration
STRIPE_SECRET_KEY=sk_live_... # or sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
STRIPE_PUBLISHABLE_KEY=pk_live_... # or pk_test_...

# PayPal Configuration  
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
PAYPAL_WEBHOOK_ID=your_paypal_webhook_id
PAYPAL_MODE=sandbox # or live

# Solana Configuration
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
SOLANA_MERCHANT_WALLET=your_solana_wallet_address
SOLANA_NETWORK=mainnet-beta # or devnet

# App Configuration
APP_URL=https://preview--koi-app3.lovable.app
SUPABASE_URL=https://arkywiwduoqpobflwssm.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# n8n Integration
N8N_WEBHOOK_URL=https://n8n.peppermintai.live/webhook/koi-app
N8N_API_KEY=your_n8n_api_key
```

## 💰 **PAYMENT PROVIDER SETUP**

### **1. STRIPE SETUP**

#### **Step 1: Create Stripe Account**
1. Go to [stripe.com](https://stripe.com)
2. Create account and complete verification
3. Get API keys from Dashboard → Developers → API keys

#### **Step 2: Configure Products & Prices**
```javascript
// Create product in Stripe Dashboard or via API
{
  "name": "Koi Premium",
  "description": "Full access to Koi productivity and gaming features",
  "prices": [
    {
      "unit_amount": 5000, // $50.00 in cents
      "currency": "usd",
      "recurring": {
        "interval": "month"
      }
    }
  ]
}
```

#### **Step 3: Configure Webhooks**
- Endpoint: `https://arkywiwduoqpobflwssm.supabase.co/functions/v1/stripe-webhook`
- Events to listen for:
  - `customer.subscription.created`
  - `customer.subscription.updated`
  - `customer.subscription.deleted`
  - `invoice.payment_succeeded`
  - `invoice.payment_failed`
  - `payment_method.attached`

### **2. PAYPAL SETUP**

#### **Step 1: Create PayPal Developer Account**
1. Go to [developer.paypal.com](https://developer.paypal.com)
2. Create app and get Client ID/Secret
3. Configure webhook endpoint

#### **Step 2: Configure Subscription Plans**
```javascript
// Create subscription plan via PayPal API
{
  "product_id": "PROD-KOI-PREMIUM",
  "name": "Koi Premium Monthly",
  "billing_cycles": [{
    "frequency": {
      "interval_unit": "MONTH",
      "interval_count": 1
    },
    "tenure_type": "REGULAR",
    "sequence": 1,
    "total_cycles": 0,
    "pricing_scheme": {
      "fixed_price": {
        "value": "50.00",
        "currency_code": "USD"
      }
    }
  }]
}
```

### **3. SOLANA SETUP**

#### **Step 1: Create Merchant Wallet**
1. Use Phantom, Solflare, or generate programmatically
2. Fund with SOL for transaction fees
3. Set up monitoring for incoming payments

#### **Step 2: Configure Payment Monitoring**
- Monitor wallet for USDC/SOL payments
- Verify payment amounts match subscription price
- Handle transaction confirmations

## 🔐 **SECURITY CONFIGURATION**

### **PCI Compliance Checklist**
- ✅ Never store raw card data
- ✅ Use tokenized payment methods
- ✅ Implement webhook signature verification
- ✅ Encrypt sensitive data at rest
- ✅ Use HTTPS for all communications
- ✅ Implement rate limiting
- ✅ Log all payment events

### **Webhook Security**
```javascript
// Verify webhook signatures
const verifyStripeSignature = (payload, signature, secret) => {
  return stripe.webhooks.constructEvent(payload, signature, secret);
};

const verifyPayPalSignature = (payload, headers) => {
  return paypal.notification.webhookLookup.verify(payload, headers);
};
```

## 📊 **BUSINESS METRICS TO TRACK**

### **Key Performance Indicators**
- Monthly Recurring Revenue (MRR)
- Customer Acquisition Cost (CAC)
- Lifetime Value (LTV)
- Churn Rate
- Payment Success Rate
- Trial-to-Paid Conversion Rate

### **Payment Analytics**
- Revenue by payment method
- Failed payment recovery rate
- Geographic revenue distribution
- Subscription upgrade/downgrade rates

## 🌍 **INTERNATIONAL CONSIDERATIONS**

### **Currency Support**
- Primary: USD
- Consider adding: EUR, GBP, CAD, AUD
- Crypto: USDC, SOL

### **Tax Compliance**
- Implement tax calculation for EU VAT
- Handle US state sales tax
- Consider using Stripe Tax or similar service

### **Localization**
- Payment forms in multiple languages
- Local payment methods (SEPA, iDEAL, etc.)
- Currency display preferences

## 🚀 **DEPLOYMENT CHECKLIST**

### **Pre-Launch**
- [ ] Database schema deployed
- [ ] RLS policies active
- [ ] Payment provider accounts verified
- [ ] Webhook endpoints tested
- [ ] SSL certificates valid
- [ ] Environment variables set
- [ ] Backup payment method configured

### **Post-Launch Monitoring**
- [ ] Payment success rates
- [ ] Webhook delivery status
- [ ] Database performance
- [ ] Error rates and alerts
- [ ] Customer support tickets
- [ ] Revenue tracking

## 🆘 **TROUBLESHOOTING**

### **Common Issues**
1. **Webhook failures**: Check endpoint URLs and signatures
2. **Payment declines**: Implement retry logic and user notifications
3. **Subscription sync issues**: Monitor webhook processing logs
4. **Currency mismatches**: Validate currency codes
5. **Tax calculation errors**: Test with various locations

### **Support Contacts**
- Stripe Support: [support.stripe.com](https://support.stripe.com)
- PayPal Developer Support: [developer.paypal.com/support](https://developer.paypal.com/support)
- Solana Developer Resources: [docs.solana.com](https://docs.solana.com)

## 📈 **OPTIMIZATION STRATEGIES**

### **Conversion Optimization**
- A/B test payment forms
- Optimize checkout flow
- Implement smart payment routing
- Add social proof and security badges
- Offer multiple payment options

### **Churn Reduction**
- Failed payment recovery emails
- Dunning management
- Pause subscription option
- Win-back campaigns
- Exit intent surveys

### **Revenue Optimization**
- Annual subscription discounts
- Usage-based pricing tiers
- Add-on features
- Enterprise plans
- Referral programs
