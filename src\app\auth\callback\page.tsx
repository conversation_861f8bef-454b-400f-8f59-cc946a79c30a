'use client';

import { useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { supabase } from '../../../lib/supabase/client';

export default function AuthCallback() {
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        console.log('Auth callback triggered');
        console.log('Current URL:', window.location.href);

        // Get URL parameters
        const code = searchParams.get('code');
        const error = searchParams.get('error');
        const errorDescription = searchParams.get('error_description');
        const accessToken = searchParams.get('access_token');
        const refreshToken = searchParams.get('refresh_token');
        const tokenType = searchParams.get('token_type');

        console.log('URL params:', { code, error, errorDescription, accessToken, refreshToken, tokenType });

        // Handle error cases first
        if (error) {
          console.error('Auth callback error:', error, errorDescription);
          let errorMessage = errorDescription || error;

          // Provide more user-friendly error messages
          if (error === 'access_denied' && errorDescription?.includes('otp_expired')) {
            errorMessage = 'Email verification link has expired. Please try signing up again.';
          }

          router.push('/auth?error=' + encodeURIComponent(errorMessage));
          return;
        }

        // Handle different auth flows
        if (code) {
          // PKCE flow - exchange code for session
          console.log('Exchanging code for session...');
          const { data, error: exchangeError } = await supabase.auth.exchangeCodeForSession(code);

          if (exchangeError) {
            console.error('Code exchange error:', exchangeError);
            router.push('/auth?error=' + encodeURIComponent(exchangeError.message));
            return;
          }

          if (data.session) {
            console.log('Authentication successful via code exchange');
            router.push('/');
            return;
          }
        } else if (accessToken && refreshToken) {
          // Legacy flow - set session directly
          console.log('Setting session from tokens...');
          const { data, error: sessionError } = await supabase.auth.setSession({
            access_token: accessToken,
            refresh_token: refreshToken
          });

          if (sessionError) {
            console.error('Session set error:', sessionError);
            router.push('/auth?error=' + encodeURIComponent(sessionError.message));
            return;
          }

          if (data.session) {
            console.log('Authentication successful via token set');
            router.push('/');
            return;
          }
        }

        // Fallback: check for existing session
        console.log('Checking for existing session...');
        const { data: sessionData, error: sessionError } = await supabase.auth.getSession();

        if (sessionError) {
          console.error('Session check error:', sessionError);
          router.push('/auth?error=' + encodeURIComponent(sessionError.message));
          return;
        }

        if (sessionData.session) {
          console.log('Found existing session, redirecting to dashboard');
          router.push('/');
        } else {
          console.log('No session found, redirecting to auth page');
          router.push('/auth');
        }
      } catch (error) {
        console.error('Unexpected error in auth callback:', error);
        router.push('/auth?error=unexpected_error');
      }
    };

    handleAuthCallback();
  }, [router, searchParams]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 flex items-center justify-center">
      <div className="text-center">
        {/* Animated Koi Logo */}
        <div className="w-16 h-16 bg-gradient-to-br from-cyan-300 via-blue-400 to-purple-500 rounded-full flex items-center justify-center shadow-2xl mx-auto mb-6 animate-koi-bob border-2 border-white/20">
          {/* Koi Fish SVG */}
          <svg width="40" height="40" viewBox="0 0 100 100" className="drop-shadow-lg">
            {/* Koi body - cyan to purple gradient */}
            <ellipse cx="40" cy="50" rx="25" ry="20" fill="url(#koiGradientCallback)" />

            {/* Tail fin */}
            <path d="M15 50 Q5 40 10 30 Q15 35 20 40 Q15 45 10 50 Q15 55 20 60 Q15 65 10 70 Q5 60 15 50" fill="url(#finGradientCallback)" />

            {/* Top fin */}
            <path d="M35 30 Q30 20 40 25 Q45 30 35 30" fill="url(#finGradientCallback)" />

            {/* Side fins */}
            <ellipse cx="30" cy="60" rx="8" ry="4" fill="url(#finGradientCallback)" transform="rotate(30 30 60)" />
            <ellipse cx="50" cy="60" rx="8" ry="4" fill="url(#finGradientCallback)" transform="rotate(-30 50 60)" />

            {/* Eyes */}
            <circle cx="45" cy="45" r="4" fill="white" />
            <circle cx="45" cy="45" r="2.5" fill="black" />
            <circle cx="46" cy="44" r="1" fill="white" />

            {/* Spots */}
            <circle cx="35" cy="40" r="2" fill="#90EE90" opacity="0.8" />
            <circle cx="40" cy="35" r="1.5" fill="#90EE90" opacity="0.8" />
            <circle cx="30" cy="50" r="1.5" fill="#90EE90" opacity="0.8" />

            {/* Mouth */}
            <path d="M55 50 Q60 52 55 54" stroke="black" strokeWidth="1" fill="none" />

            {/* Gradients */}
            <defs>
              <linearGradient id="koiGradientCallback" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#00FFFF" />
                <stop offset="50%" stopColor="#0080FF" />
                <stop offset="100%" stopColor="#8000FF" />
              </linearGradient>
              <linearGradient id="finGradientCallback" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#FF69B4" />
                <stop offset="50%" stopColor="#DA70D6" />
                <stop offset="100%" stopColor="#9370DB" />
              </linearGradient>
            </defs>
          </svg>
        </div>
        
        <h2 className="text-2xl font-bold text-white mb-4">Authenticating...</h2>
        <p className="text-blue-200">Please wait while we sign you in.</p>
        
        {/* Loading spinner */}
        <div className="mt-6">
          <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin mx-auto"></div>
        </div>
      </div>
    </div>
  );
}
